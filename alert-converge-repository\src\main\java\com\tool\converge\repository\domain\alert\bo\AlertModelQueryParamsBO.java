package com.tool.converge.repository.domain.alert.bo;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tool.converge.repository.domain.common.PageParamsBO;
import com.tool.converge.repository.domain.alert.db.AlertModelDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 预警模型
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-06 14:45:26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "AlertModelQueryParamsBO对象", description = "预警模型")
public class AlertModelQueryParamsBO extends PageParamsBO<AlertModelDO> implements Serializable{

    private static final long serialVersionUID = 1L;


    @Schema(description = "模型id")
    private Long id;

    @Schema(description = "预警名称")
    private String modelName;

    @Schema(description = "模型编码")
    private String modelCode;

    @Schema(description = "业务类型")
    private String businessType;

    @Schema(description = "预警类型")
    private String alertType;

    @Schema(description = "是否关联规则 0为不关联，1为关联")
    private Boolean related;

    @Schema(description = "是否生成预警消息 0为不生成，1为生成")
    private Boolean warned;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "创建时间-开始")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间-结束")
    private LocalDateTime createTimeEnd;

    @Schema(description = "修改时间-开始")
    private LocalDateTime updateTimeStart;

    @Schema(description = "修改时间-结束")
    private LocalDateTime updateTimeEnd;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    private Boolean deleted;

    @Override
    public LambdaQueryWrapper<AlertModelDO> queryWrapper() {

        LambdaQueryWrapper<AlertModelDO> query = new LambdaQueryWrapper<>();

        query.eq(id!=null,AlertModelDO::getId,id);

        query.like(StringUtils.isNotBlank(modelName),AlertModelDO::getModelName,modelName);

        query.like(StringUtils.isNotBlank(modelCode),AlertModelDO::getModelCode,modelCode);

        query.eq(StringUtils.isNotBlank(businessType),AlertModelDO::getBusinessType,businessType);

        query.eq(StringUtils.isNotBlank(alertType),AlertModelDO::getAlertType,alertType);

        query.eq(related!=null,AlertModelDO::getRelated,related);

        query.eq(warned!=null,AlertModelDO::getWarned,warned);

        query.eq(StringUtils.isNotBlank(description),AlertModelDO::getDescription,description);

        query.ge(createTimeStart != null, AlertModelDO::getCreateTime, createTimeStart);

        query.le(createTimeEnd != null, AlertModelDO::getCreateTime, createTimeEnd);

        query.ge(updateTimeStart != null, AlertModelDO::getUpdateTime, updateTimeStart);

        query.le(updateTimeEnd != null, AlertModelDO::getUpdateTime, updateTimeEnd);

        query.eq(StringUtils.isNotBlank(creator),AlertModelDO::getCreator,creator);

        query.eq(StringUtils.isNotBlank(updater),AlertModelDO::getUpdater,updater);

        query.eq(deleted!=null,AlertModelDO::getDeleted,deleted);

        return query;
    }
}
