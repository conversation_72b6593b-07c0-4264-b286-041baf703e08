package com.tool.converge.business.alert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tool.converge.repository.domain.alert.bo.*;
import com.tool.converge.repository.domain.alert.db.AlertEventDO;
import com.tool.converge.repository.domain.alert.db.AlertMessageDO;
import com.tool.converge.repository.domain.alert.vo.AlertMessageDetailVO;
import com.tool.converge.repository.domain.alert.vo.AlertMessagePageVO;
import org.checkerframework.checker.units.qual.A;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 15:15 2025/8/5
 * @Description TODO
 * @MethodName  预警消息 服务类
 * @param
 * @return null
 */
public interface AlertMessageService extends IService<AlertMessageDO> {

    /**
     * 添加预警消息
     *
     * @param saveBO 保存参数
     * @return 是否成功
     */
    Boolean saveInfo(AlertMessageSaveBO saveBO);

    /**
     * 通过ID获取预警消息详情
     *
     * @param id 主键ID
     * @return 详情信息
     */
    AlertMessageDetailVO getInfo(Long id);

    /**
     * 分页获取预警消息列表
     *
     * @param queryParamsBO 查询参数
     * @return 分页结果
     */
    IPage<AlertMessagePageVO> getPageInfo(AlertMessageQueryParamsBO queryParamsBO);

    /**
     * 导出预警消息
     *
     * @param alertMessageExportBO 导出参数
     * @param httpServletResponse 响应对象
     */
    void export(AlertMessageExportBO alertMessageExportBO, HttpServletResponse httpServletResponse);

    /**
     * 预警消息发送
     *
     * @param alertEventDO 发送参数
     */
    void send(AlertEventDO alertEventDO);
}
