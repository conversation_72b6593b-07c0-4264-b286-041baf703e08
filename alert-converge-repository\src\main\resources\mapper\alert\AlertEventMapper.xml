<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tool.converge.repository.mapper.alert.AlertEventMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tool.converge.repository.domain.alert.db.AlertEventDO">
        <id column="id" property="id" />
        <id column="model_code" property="modelCode" />
        <result column="event_id" property="eventId" />
        <result column="system_code" property="systemCode" />
        <result column="platform_name" property="platformName" />
        <result column="md5" property="md5" />
        <result column="period" property="period" />
        <result column="reason" property="reason" />
        <result column="event_status" property="eventStatus" />
        <result column="service_no" property="serviceNo" />
        <result column="index_value" property="indexValue" />
        <result column="payload" property="payload" />
        <result column="state" property="state" />
        <result column="handle_status" property="handleStatus" />
        <result column="problem_result" property="problemResult" />
        <result column="related" property="related" />
        <result column="alert_time" property="alertTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creator" property="creator" />
        <result column="updater" property="updater" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <resultMap id="extendResultMap" type="com.tool.converge.repository.domain.alert.vo.AlertEventPageVO" extends="BaseResultMap">
        <result column="model_name" property="modelName" />
        <result column="business_type" property="businessType" />
        <result column="alert_type" property="alertType" />
        <result column="system_name" property="systemName" />
        <result column="alert_level" property="alertLevel" />

    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, event_id, system_code, model_code, model_name, business_type, alert_type, system_name, platform_name, md5, period, reason, event_status, service_no, index_value, payload, state, handle_status, problem_result, related, alert_time, create_time, update_time, creator, updater, deleted
    </sql>


    <select id="selectEventPage" resultMap="BaseResultMap" parameterType="com.tool.converge.repository.domain.alert.db.AlertEventDO">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        t_alert_event ae
        <where>
            <if test="eventDo.systemCode != null and eventDo.systemCode !=''">
                AND ae.system_code = #{eventDo.systemCode}
            </if>
            <if test="eventDo.modelCode != null and eventDo.modelCode !=''">
                AND ae.model_code = #{eventDo.modelCode}
            </if>
            <if test="eventDo.eventStatus != null and eventDo.eventStatus != ''">
                AND ae.event_status = #{eventDo.eventStatus}
            </if>
            <if test="eventDo.platformName != null and eventDo.platformName !='' ">
                AND ae.platform_name like concat('%',#{eventDo.platformName},'%')
            </if>
            <if test="eventDo.eventId != null and eventDo.eventId !=''">
                AND ae.event_id like concat('%',#{eventDo.eventId},'%')
            </if>
            <if test="eventDo.businessType != null and eventDo.businessType != ''">
                AND ae.business_type = #{eventDo.businessType}
            </if>
            <if test="eventDo.alertType != null and eventDo.alertType != ''">
                AND ae.alert_type = #{eventDo.alertType}
            </if>
            <if test="eventDo.state != null  and eventDo.state !=''">
                AND ae.state = #{eventDo.state}
            </if>
            <if test="eventDo.startTime">
                AND ae.create_time &gt;= #{eventDo.startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="eventDo.endTime">
                AND ae.create_time &lt;= #{eventDo.endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="eventDo.systemName != null and eventDo.systemName !=''">
                AND ae.system_name = #{eventDo.systemName}
            </if>
            <if test="eventDo.modelName != null and eventDo.modelName !=''">
                AND ae.model_name = #{eventDo.modelName}
            </if>
            <if test="eventDo.serviceNo != null and eventDo.serviceNo !=''">
                AND ae.service_no like concat('%',#{eventDo.serviceNo},'%')
            </if>
            <if test="eventDo.related != null">
                AND ae.related = #{eventDo.related}
            </if>
            <if test="eventDo.alertTimeStart != null">
                AND ae.alert_time &gt;= #{eventDo.alertTimeStart,jdbcType=TIMESTAMP}
            </if>
            <if test="eventDo.alertTimeEnd != null">
                AND ae.alert_time &lt;= #{eventDo.alertTimeEnd,jdbcType=TIMESTAMP}
            </if>
            AND ae.deleted = #{eventDo.deleted}

        </where>
    </select>


    <select id="selectEventDetail" resultMap="BaseResultMap" parameterType="com.tool.converge.repository.domain.alert.db.AlertEventDO">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        t_alert_event ae
        <where>
            <if test="eventDo.systemCode != null">
                AND ae.system_code = #{eventDo.systemCode}
            </if>
            <if test="eventDo.modelCode != null">
                AND ae.model_code = #{eventDo.modelCode}
            </if>
            <if test="eventDo.eventStatus != null">
                AND ae.event_status = #{eventDo.eventStatus}
            </if>
            <if test="eventDo.platformName != null">
                AND ae.platform_name like concat('%',#{eventDo.platformName},'%')
            </if>
            <if test="eventDo.eventId != null">
                AND ae.event_id like concat('%',#{eventDo.eventId},'%')
            </if>
            <if test="eventDo.businessType != null">
                AND ae.business_type = #{eventDo.businessType}
            </if>
            <if test="eventDo.alertType != null">
                AND ae.alert_type = #{eventDo.alertType}
            </if>
            <if test="eventDo.state">
                AND ae.state = #{eventDo.state}
            </if>
            <if test="eventDo.startTime != null">
                AND ae.create_time &gt;= #{eventDo.startTime}
            </if>
            <if test="eventDo.endTime != null">
                AND ae.create_time &lt;= #{eventDo.endTime}
            </if>
            <if test="eventDo.systemName != null">
                AND ae.system_name = #{eventDo.systemName}
            </if>
            <if test="eventDo.modelName != null">
                AND ae.model_name = #{eventDo.modelName}
            </if>
            <if test="eventDo.serviceNo != null">
                AND ae.service_no like concat('%',#{eventDo.serviceNo},'%')
            </if>
            <if test="eventDo.related != null">
                AND ae.related = #{eventDo.related}
            </if>
            <if test="eventDo.ids != null and eventDo.ids.size() > 0">
                AND ae.id IN
                <foreach collection= "eventDo.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            AND ae.deleted = #{eventDo.deleted}
            order by ae.create_time desc limit 2000
        </where>
    </select>
</mapper>
