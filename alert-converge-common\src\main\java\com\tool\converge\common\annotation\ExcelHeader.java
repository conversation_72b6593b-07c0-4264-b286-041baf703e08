package com.tool.converge.common.annotation;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
 /**
  * <AUTHOR>
  * @date 2025/3/11 9:11
  * @Description excel表头注解
  * @MethodName
  */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ExcelHeader {

    /**
     * 表头名
     */
    String name();
    
    /**
     * 列宽度（字符数）
     * 默认值为-1，表示使用系统默认宽度
     */
    int width() default -1;
}