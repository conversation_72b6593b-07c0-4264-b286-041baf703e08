package com.tool.converge.common.utils;

import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.hzed.structure.common.exception.ServiceException;
import com.tool.converge.common.annotation.ExcelHeader;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static cn.hutool.core.bean.BeanUtil.getFieldValue;

 /**
  * <AUTHOR>
  * @date 2025/3/11 10:56
  * @Description 导出工具类
  * @param TODO
  * @MethodName
  */
 @Slf4j
public class ExcelUtils {

    private static final String FILE_EXTENSION_XLS = ".xls";
    private static final String FILE_EXTENSION_XLSX = ".xlsx";


     /**
     * 写入数据到Excel文件。
     *
     * @param fileName 文件路径。
     * @param data     要写入的数据列表。
     * @param orderByFields     排序字段名。
     * @param resp
     * @throws IOException 如果文件写入失败。
     */
    public static <T> void writeExcel(List<T> data, Class<T> clazz, List<String> orderByFields, String fileName, HttpServletResponse resp) {
        try (ExcelWriter excelWriter = ExcelUtil.getWriter(true);
             ServletOutputStream outputStream = resp.getOutputStream()) {

            LinkedHashMap<String, String> headerList = getHeaderList(clazz, orderByFields);

            excelWriter.writeHeadRow(headerList.keySet());
            // 设置表头样式
            // 写入表头
            writeExcelData(excelWriter, data, headerList, clazz);
            // 设置响应头content-type
            resp.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset:utf-8");

            // 设置下载文件名
            String encode = URLEncoder.encode(fileName, "UTF-8");
            resp.setHeader("Content-Disposition", "attachment;filename=" + encode + FILE_EXTENSION_XLS);


            // 输出excel文件
            excelWriter.flush(outputStream, true);
        } catch (Exception e) {
            log.error("导出预警事件异常:{}", e);
            throw new ServiceException("导出异常");
        }

    }

    public static <T> void writeExcelData(ExcelWriter excelWriter, List<T> data, LinkedHashMap<String, String> headerList, Class<T> clazz) {
        // 设置列宽，根据注解配置或默认规则
        setColumnWidths(excelWriter, headerList, clazz);
        
        // 遍历数据
        for (T t : data) {
            // 创建一个数组，用于存储当前行的数据
            List<Object> rowData = new ArrayList<>();
            for (String fieldName : headerList.values()) {
                try {
                    // 获取字段值
                    Object value = getFieldValue(t, fieldName);
                    // 对于通知人员字段，如果内容过长则进行处理
                    if ("notificationUsers".equals(fieldName) && value instanceof String) {
                        String strValue = (String) value;
                        // 如果通知人员字符串过长，可以考虑截断或换行处理
                        if (strValue != null && strValue.length() > 100) {
                            // 超过100个字符时进行换行处理
                            value = strValue.replaceAll(",", ",\n");
                        }
                    }
                    // 将字段值放入数组
                    rowData.add(value);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            excelWriter.writeRow(rowData);
        }
    }


    private static LinkedHashMap<String, String> getHeaderList(Class<?> clazz, List<String> orderByFields) {
        LinkedHashMap<String, String> headerList = new LinkedHashMap<>();
        Field[] fields = clazz.getDeclaredFields();
        Map<String, Field> fieldMap = new LinkedHashMap<>();
        for (Field field : fields) {
            field.setAccessible(true);
            if (field.isAnnotationPresent(ExcelHeader.class)) {
                fieldMap.put(field.getName(), field);
            }
        }

        if (CollectionUtils.isNotEmpty(orderByFields)) {
            for (String orderByField : orderByFields) {
                Field field = fieldMap.get(orderByField);
                if (field == null) {
                    continue;
                }
                ExcelHeader annotation = field.getAnnotation(ExcelHeader.class);
                if (annotation != null) {
                    headerList.put(annotation.name(), orderByField);
                    fieldMap.remove(orderByField);
                }
            }
        }
        // 如果未有指定排序字段，则按照字段声明顺序添加到headerList中
        fieldMap.values().forEach(field -> {
            headerList.put(field.getAnnotation(ExcelHeader.class).name(), field.getName());
        });
        return headerList;
    }
    
    /**
     * 设置Excel列宽
     * @param excelWriter Excel写入器
     * @param headerList 表头列表
     * @param clazz 数据类
     */
    private static <T> void setColumnWidths(ExcelWriter excelWriter, LinkedHashMap<String, String> headerList, Class<T> clazz) {
        Field[] fields = clazz.getDeclaredFields();
        Map<String, Field> fieldMap = new LinkedHashMap<>();
        for (Field field : fields) {
            field.setAccessible(true);
            if (field.isAnnotationPresent(ExcelHeader.class)) {
                fieldMap.put(field.getName(), field);
            }
        }
        
        int columnIndex = 0;
        for (Map.Entry<String, String> entry : headerList.entrySet()) {
            String headerName = entry.getKey();
            String fieldName = entry.getValue();
            
            Field field = fieldMap.get(fieldName);
            if (field != null && field.isAnnotationPresent(ExcelHeader.class)) {
                ExcelHeader annotation = field.getAnnotation(ExcelHeader.class);
                int width = annotation.width();
                
                if (width > 0) {
                    // 使用注解中配置的宽度
                    excelWriter.setColumnWidth(columnIndex, width);
                } else {
                    // 使用默认规则设置宽度
                    excelWriter.setColumnWidth(columnIndex, 15);
                }
            } else {
                // 使用默认规则设置宽度
                excelWriter.setColumnWidth(columnIndex, 15);
            }
            columnIndex++;
        }
    }
}