package com.tool.converge.business.alert.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hzed.structure.common.exception.ServiceException;
import com.hzed.structure.redis.util.RedisUtil;
import com.tool.converge.business.alert.AlertEventService;
import com.tool.converge.business.alert.AlertMessageService;
import com.tool.converge.business.alert.AlertModelService;
import com.tool.converge.business.source.DataSourceService;
import com.tool.converge.business.system.SysDictValueService;
import com.tool.converge.common.constant.KeyConstant;
import com.tool.converge.common.utils.ExcelUtils;
import com.tool.converge.common.utils.SnowflakeIdUtil;
import com.tool.converge.repository.domain.alert.bo.*;
import com.tool.converge.repository.domain.alert.db.AlertEventDO;
import com.tool.converge.repository.domain.alert.db.AlertModelDO;
import com.tool.converge.repository.domain.alert.vo.AlertEventExportVO;
import com.tool.converge.repository.domain.alert.vo.AlertEventPageVO;
import com.tool.converge.repository.domain.source.db.DataSourceDO;
import com.tool.converge.repository.domain.system.db.SysDictValueDO;
import com.tool.converge.repository.mapper.alert.AlertEventMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 预警事件 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10 09:19:00
 */
@Slf4j
@Service
public class AlertEventServiceImpl extends ServiceImpl<AlertEventMapper, AlertEventDO> implements AlertEventService {

    @Resource
    private AlertEventMapper alertEventMapper;

    @Resource
    @Lazy
    private AlertEventService self;

    @Resource
    private SysDictValueService sysDictValueService;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private AlertMessageService alertMessageService;

    @Resource
    @Lazy
    private DataSourceService dataSourceService;

    @Resource
    @Lazy
    private AlertModelService alertModelService;

    @Override
    public LocalDateTime submit(AlertEventSdkBO submitEventBO) {
        List<AlertEventDO> alertEventDoS = alertEventMapper.selectList(new LambdaQueryWrapper<AlertEventDO>().eq(AlertEventDO::getMd5, submitEventBO.getMd5()));
        if (CollectionUtil.isNotEmpty(alertEventDoS)) {
                return alertEventDoS.get(0).getCreateTime();
        }
        LocalDateTime now = LocalDateTime.now();
        asyncInsertAlertEvent(submitEventBO,now);
        return now;
    }

    @Override
    public LocalDateTime report(AlertEventReportBO reportEventBO) {
        LocalDateTime now = LocalDateTime.now();
        asyncInsertReportEvent(reportEventBO, now);
        return now;
    }

    @Override
    public void asyncInsertAlertEvent(AlertEventSdkBO submitEventBO, LocalDateTime submitTime) {
        // 构建AlertEventDO实体
        AlertEventDO entity = new AlertEventDO();
        BeanUtils.copyProperties(submitEventBO, entity);
        entity.setModelCode(submitEventBO.getModelId());
        entity.setSystemCode(submitEventBO.getSystemId());
        entity.setCreateTime(submitTime);
        entity.setAlertTime(submitTime);

        // 查询并填充冗余字段
        fillRedundantFields(entity);

        // 生成Redis锁键值
        String lockKey = KeyConstant.ALERT_EVENT_SUBMIT + submitEventBO.getMd5();

        // 调用通用异步插入方法
        self.asyncInsertEvent(entity, lockKey,1);
    }

    @Override
    public void asyncInsertReportEvent(AlertEventReportBO reportEventBO, LocalDateTime submitTime) {
        //校验systemId和modelId是否存在
        List<DataSourceDO> systems = dataSourceService.list(new LambdaQueryWrapper<DataSourceDO>().eq(DataSourceDO::getSystemCode, reportEventBO.getSystemId()));
        if (CollectionUtil.isEmpty(systems)) {
            throw new ServiceException(String.format("systemId[%s]找不到对应的数据源", reportEventBO.getSystemId()));
        }
        List<AlertModelDO> models = alertModelService.list(new LambdaQueryWrapper<AlertModelDO>().eq(AlertModelDO::getModelCode, reportEventBO.getModelId()));
        if (CollectionUtil.isEmpty(models)) {
            throw new ServiceException(String.format("modelId[%s]找不到对应的预警模型",reportEventBO.getModelId()));
        }
        // 生成事件ID和业务编号
        String eventId = SnowflakeIdUtil.generateIdStr();
        String serviceNo = reportEventBO.getModelId() + "_" + submitTime.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));

        // 将payload转换为JSON字符串
        String payloadJson = "";
        try {
            payloadJson = objectMapper.writeValueAsString(reportEventBO.getPayload());
        } catch (Exception e) {
            log.error("转换payload为JSON失败", e);
            payloadJson = reportEventBO.getPayload().toString();
        }
        //资方名称
        String fundProviderName = reportEventBO.getPayload().stream().filter(payloadItem -> "fund_provider_name".equals(payloadItem.getCode()))
                .findFirst().map(AlertEventReportBO.PayloadItem::getValue).orElse(null);
        // 创建AlertEventDO实体
        AlertEventDO entity = AlertEventDO.builder()
                .eventId(eventId)
                .systemCode(reportEventBO.getSystemId())
                .systemName(systems.get(0).getSystemName())
                .modelCode(reportEventBO.getModelId())
                .modelName(models.get(0).getModelName())
                .businessType(models.get(0).getBusinessType())
                .alertType(models.get(0).getAlertType())
                .serviceNo(serviceNo)
                .payload(payloadJson)
                .platformName(fundProviderName)
                .related(models.get(0).getRelated())
                .alertTime(reportEventBO.getEventTime())
                .build();

        // 调用通用异步插入方法（不使用Redis锁）
        self.asyncInsertEvent(entity, null,2);
    }

    /**
     * 通用异步插入预警事件
     *
     * @param entity 预警事件实体
     * @param lockKey Redis锁键值（可为null表示不使用锁）
     * @param version 版本号
     */
    @Async("Alert-Event-Submit")
    @Override
    public void asyncInsertEvent(AlertEventDO entity, String lockKey,Integer version) {
        boolean useLock = StringUtils.isNotBlank(lockKey);

        // 如果需要使用锁，先检查锁
        if (useLock) {
            if (Boolean.TRUE.equals(RedisUtil.exists(lockKey))) {
                log.error("已存在正在上报事件，事件业务编号:{}, 事件ID:{}", entity.getServiceNo(), entity.getEventId());
                throw new ServiceException("已存在正在上报事件，事件业务编号:" + entity.getServiceNo());
            }
        }

        try {
            // 设置锁
            if (useLock) {
                RedisUtil.setNx(lockKey, "", KeyConstant.THREE_HUNDRED, TimeUnit.SECONDS);
            }

            // 插入数据
            if (!SqlHelper.retBool(alertEventMapper.insert(entity))) {
                log.error("异步插入预警事件失败，eventId={},serviceNo={}", entity.getEventId(), entity.getServiceNo());
            } else {
                log.info("成功插入预警事件，eventId={},serviceNo={}", entity.getEventId(), entity.getServiceNo());
            }
        } catch (Exception e) {
            log.error("异步插入预警事件失败，serviceNo={}", entity.getServiceNo(), e);
        } finally {
            // 释放锁
            if (useLock) {
                RedisUtil.del(lockKey);
            }
        }
        final int two=2;
        if (version!=null &&  two == version) {
            alertMessageService.send(entity);
        }
    }

    @Override
    public Boolean delInfo(Long id) {
        return SqlHelper.retBool(alertEventMapper.deleteById(id));
    }

    @Override
    public Boolean updateInfo(AlertEventUpdateBO updateBO) {
        AlertEventDO entity = new AlertEventDO();
        BeanUtils.copyProperties(updateBO, entity);
        return SqlHelper.retBool(alertEventMapper.updateById(entity));
    }

    @Override
    public void export(AlertEventExportBo alertEventExportBo, HttpServletResponse httpServletResponse) {
        List<AlertEventDO> alertEventDoS = alertEventMapper.selectEventDetail(AlertEventDO.builder().ids(alertEventExportBo.getIds()).deleted(false).build());
        // 业务类型
        Map<String, String> businessType = sysDictValueService.listByKeyName("business_type").stream().collect(Collectors.toMap(SysDictValueDO::getValue, SysDictValueDO::getLabel));
        // 预警类型
        Map<String, String> alertType = sysDictValueService.listByKeyName("alert_type").stream().collect(Collectors.toMap(SysDictValueDO::getValue, SysDictValueDO::getLabel));
        // 状态值
        Map<String, String> alertState = sysDictValueService.listByKeyName("alert_state").stream().collect(Collectors.toMap(SysDictValueDO::getValue, SysDictValueDO::getLabel));
        // 事件状态
        Map<String, String> eventStatus = sysDictValueService.listByKeyName("alert_event_status").stream().collect(Collectors.toMap(SysDictValueDO::getValue, SysDictValueDO::getLabel));

        // 循环设置参数
        List<AlertEventExportVO> eventExportVoS = alertEventDoS.stream().map(eventDO -> {
            AlertEventExportVO alertEventExportVO = new AlertEventExportVO();
            BeanUtils.copyProperties(eventDO, alertEventExportVO);
            alertEventExportVO.setState(alertState.get(alertEventExportVO.getState()));
            alertEventExportVO.setEventStatus(eventStatus.get(alertEventExportVO.getState()));
            alertEventExportVO.setBusinessType(businessType.get(alertEventExportVO.getBusinessType()));
            alertEventExportVO.setAlertType(alertType.get(alertEventExportVO.getAlertType()));
            return alertEventExportVO;
        }).collect(Collectors.toList());
        //导出
        ExcelUtils.writeExcel(eventExportVoS, AlertEventExportVO.class, alertEventExportBo.getOrderByColumns(), LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS")), httpServletResponse);
    }


    @Override
    public IPage<AlertEventPageVO> getPageInfo(AlertEventQueryParamsBO queryParamsBO) {
        AlertEventDO alertEventDO =new AlertEventDO();
        BeanUtils.copyProperties(queryParamsBO, alertEventDO);
        if (alertEventDO.getDeleted() == null) {
            alertEventDO.setDeleted(false);
        }
        if (StringUtils.isBlank(queryParamsBO.getOrderFields())) {
            queryParamsBO.setOrderFields("createTime");
        }
        if (StringUtils.isBlank(queryParamsBO.getOrderRules())) {
            queryParamsBO.setOrderRules("desc");
        }
        IPage<AlertEventPageVO> convert = alertEventMapper.selectEventPage(queryParamsBO.pageInfo(), alertEventDO).convert(AlertEventPageVO::of);
        if (CollectionUtil.isNotEmpty(convert.getRecords())) {
            List<AlertEventPageVO> records = convert.getRecords();
             // 业务类型
            Map<String, String> businessType = sysDictValueService.listByKeyName("business_type").stream().collect(Collectors.toMap(SysDictValueDO::getValue, SysDictValueDO::getLabel));
            // 预警类型
            Map<String, String> alertType = sysDictValueService.listByKeyName("alert_type").stream().collect(Collectors.toMap(SysDictValueDO::getValue, SysDictValueDO::getLabel));
            // 状态值
            Map<String, String> alertState = sysDictValueService.listByKeyName("alert_state").stream().collect(Collectors.toMap(SysDictValueDO::getValue, SysDictValueDO::getLabel));
            // 事件状态
            Map<String, String> eventStatus = sysDictValueService.listByKeyName("alert_event_status").stream().collect(Collectors.toMap(SysDictValueDO::getValue, SysDictValueDO::getLabel));
            records.forEach(eventVo -> {
                eventVo.setState(alertState.get(eventVo.getState()));
                eventVo.setEventStatus(eventStatus.get(eventVo.getState()));
                eventVo.setAlertType(alertType.get(eventVo.getAlertType()));
                eventVo.setBusinessType(businessType.get(eventVo.getBusinessType()));
            });
        }
        return convert;
    }

    /**
     * 填充冗余字段
     *
     * @param entity 预警事件实体
     */
    private void fillRedundantFields(AlertEventDO entity) {
        // 查询预警模型信息
        if (StringUtils.isNotBlank(entity.getModelCode())) {
            List<AlertModelDO> models = alertModelService.list(
                new LambdaQueryWrapper<AlertModelDO>()
                    .eq(AlertModelDO::getModelCode, entity.getModelCode())
                    .eq(AlertModelDO::getDeleted, false)
            );
            if (CollectionUtil.isNotEmpty(models)) {
                AlertModelDO model = models.get(0);
                entity.setModelName(model.getModelName());
                entity.setBusinessType(model.getBusinessType());
                entity.setAlertType(model.getAlertType());
            }
        }

        // 查询数据源信息
        if (StringUtils.isNotBlank(entity.getSystemCode())) {
            List<DataSourceDO> systems = dataSourceService.list(
                new LambdaQueryWrapper<DataSourceDO>()
                    .eq(DataSourceDO::getSystemCode, entity.getSystemCode())
                    .eq(DataSourceDO::getDeleted, false)
            );
            if (CollectionUtil.isNotEmpty(systems)) {
                entity.setSystemName(systems.get(0).getSystemName());
            }
        }
    }

}
