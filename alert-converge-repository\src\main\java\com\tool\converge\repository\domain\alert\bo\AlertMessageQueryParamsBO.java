package com.tool.converge.repository.domain.alert.bo;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tool.converge.repository.domain.common.PageParamsBO;
import com.tool.converge.repository.domain.alert.db.AlertMessageDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 预警消息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "AlertMessageQueryParamsBO对象", description = "预警消息")
public class AlertMessageQueryParamsBO extends PageParamsBO<AlertMessageDO> implements Serializable{

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "预警事件编号，SDK生成")
    private String eventId;

    @Schema(description = "模型编码")
    private String modelCode;

    @Schema(description = "预警名称")
    private String modelName;

    @Schema(description = "预警类型")
    private String alertType;

    @Schema(description = "第三方名称")
    private String platformName;

    @Schema(description = "期次")
    private String period;

    @Schema(description = "业务唯一编号")
    private String serviceNo;

    @Schema(description = "业务类型")
    private String businessType;

    @Schema(description = "状态值")
    private String state;

    @Schema(description = "指标值")
    private String indexValue;

    @Schema(description = "原因")
    private String reason;

    @Schema(description = "规则id")
    private Long ruleId;

    @Schema(description = "预警规则")
    private String ruleName;

    @Schema(description = "是否关联规则 0为不关联，1为关联")
    private Boolean related;

    @Schema(description = "预警级别")
    private String warnLevel;

    @Schema(description = "系统编号")
    private String systemCode;

    @Schema(description = "系统名称")
    private String systemName;

    @Schema(description = "预警配置id")
    private Long configId;

    @Schema(description = "预警频率 0为不限制")
    private Integer frequency;

    @Schema(description = "通知方式 1:钉钉，2:短信，3:钉钉+短信")
    private Integer warnType;

    @Schema(description = "通知人员")
    private String notificationUsers;

    @Schema(description = "上报时间-开始")
    private LocalDateTime alertTimeStart;

    @Schema(description = "上报时间-结束")
    private LocalDateTime alertTimeEnd;

    @Schema(description = "创建时间-开始")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间-结束")
    private LocalDateTime createTimeEnd;

    @Schema(description = "修改时间-开始")
    private LocalDateTime updateTimeStart;

    @Schema(description = "修改时间-结束")
    private LocalDateTime updateTimeEnd;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    private Boolean deleted;

    @Override
    public LambdaQueryWrapper<AlertMessageDO> queryWrapper() {

        LambdaQueryWrapper<AlertMessageDO> query = new LambdaQueryWrapper<>();

        query.eq(id!=null, AlertMessageDO::getId, id);

        query.eq(StringUtils.isNotBlank(eventId), AlertMessageDO::getEventId, eventId);

        query.eq(StringUtils.isNotBlank(modelCode), AlertMessageDO::getModelCode, modelCode);

        query.like(StringUtils.isNotBlank(modelName), AlertMessageDO::getModelName, modelName);

        query.eq(StringUtils.isNotBlank(alertType), AlertMessageDO::getAlertType, alertType);

        query.like(StringUtils.isNotBlank(platformName), AlertMessageDO::getPlatformName, platformName);

        query.eq(StringUtils.isNotBlank(period), AlertMessageDO::getPeriod, period);

        query.eq(StringUtils.isNotBlank(serviceNo), AlertMessageDO::getServiceNo, serviceNo);

        query.eq(StringUtils.isNotBlank(businessType), AlertMessageDO::getBusinessType, businessType);

        query.eq(StringUtils.isNotBlank(state), AlertMessageDO::getState, state);

        query.like(StringUtils.isNotBlank(indexValue), AlertMessageDO::getIndexValue, indexValue);

        query.like(StringUtils.isNotBlank(reason), AlertMessageDO::getReason, reason);

        query.eq(ruleId!=null, AlertMessageDO::getRuleId, ruleId);

        query.like(StringUtils.isNotBlank(ruleName), AlertMessageDO::getRuleName, ruleName);

        query.eq(related!=null, AlertMessageDO::getRelated, related);

        query.eq(StringUtils.isNotBlank(warnLevel), AlertMessageDO::getWarnLevel, warnLevel);

        query.eq(StringUtils.isNotBlank(systemCode), AlertMessageDO::getSystemCode, systemCode);

        query.like(StringUtils.isNotBlank(systemName), AlertMessageDO::getSystemName, systemName);

        query.eq(configId!=null, AlertMessageDO::getConfigId, configId);

        query.eq(frequency!=null, AlertMessageDO::getFrequency, frequency);

        query.eq(warnType!=null, AlertMessageDO::getWarnType, warnType);

        query.like(StringUtils.isNotBlank(notificationUsers), AlertMessageDO::getNotificationUsers, notificationUsers);

        query.ge(alertTimeStart!=null, AlertMessageDO::getAlertTime, alertTimeStart);

        query.le(alertTimeEnd!=null, AlertMessageDO::getAlertTime, alertTimeEnd);

        query.ge(createTimeStart!=null, AlertMessageDO::getCreateTime, createTimeStart);

        query.le(createTimeEnd!=null, AlertMessageDO::getCreateTime, createTimeEnd);

        query.ge(updateTimeStart!=null, AlertMessageDO::getUpdateTime, updateTimeStart);

        query.le(updateTimeEnd!=null, AlertMessageDO::getUpdateTime, updateTimeEnd);

        query.eq(StringUtils.isNotBlank(creator), AlertMessageDO::getCreator, creator);

        query.eq(StringUtils.isNotBlank(updater), AlertMessageDO::getUpdater, updater);

        query.eq(deleted!=null, AlertMessageDO::getDeleted, deleted);

        query.orderByDesc(AlertMessageDO::getAlertTime);

        return query;
    }
}
