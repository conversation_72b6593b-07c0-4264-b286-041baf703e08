package com.tool.converge.business.alert.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tool.converge.repository.domain.alert.bo.AlertEventReportBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Payload字段处理工具类
 * 用于处理预警消息中的payload扩展字段转换
 * 
 * <AUTHOR>
 * @since 2025-08-25
 */
@Slf4j
@Component
public class PayloadUtils {

    @Resource
    private ObjectMapper objectMapper;

    /**
     * 将payload JSON字符串转换为显示格式
     * 原格式：[{"code": "hourly_total_orders", "name": "小时终态订单总数", "value": "6"}]
     * 转换后：小时终态订单总数:6
     * 
     * @param payloadJson payload JSON字符串
     * @return 转换后的显示字符串
     */
    public String convertPayloadForDisplay(String payloadJson) {
        if (StringUtils.isBlank(payloadJson)) {
            return "";
        }

        try {
            // 解析JSON字符串为PayloadItem列表
            List<AlertEventReportBO.PayloadItem> payloadItems = objectMapper.readValue(
                payloadJson, 
                new TypeReference<List<AlertEventReportBO.PayloadItem>>() {}
            );

            if (payloadItems == null || payloadItems.isEmpty()) {
                log.warn("解析后的PayloadItem列表为空");
                return "";
            }

            // 过滤掉fund_provider_name，并转换为显示格式
            String result = payloadItems.stream()
                .filter(item -> item != null && StringUtils.isNotBlank(item.getCode()))
                .filter(item -> !"fund_provider_name".equals(item.getCode()))
                .map(item -> {
                    String name = StringUtils.isNotBlank(item.getName()) ? item.getName() : item.getCode();
                    String value = StringUtils.isNotBlank(item.getValue()) ? item.getValue() : "";
                    return "\"" + name + "\":\"" + value + "\"";
                })
                .collect(Collectors.joining("\n"));

            log.debug("成功转换payload，原始数据：{}，转换结果：{}", payloadJson, result);
            return result;

        } catch (Exception e) {
            log.error("转换payload失败，原始数据：{}", payloadJson, e);
            // 转换失败时返回原始数据
            return payloadJson;
        }
    }
}
